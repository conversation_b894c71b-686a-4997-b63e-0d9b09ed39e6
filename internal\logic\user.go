package logic

import (
	"context"
	"errors"
	"fmt"
	"strings"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/pkg"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// userLogic 用户业务逻辑实现
type userLogic struct {
	userRepo    repositories.IUserRepo
	fileService service.IFileService
}

// NewUserLogic 创建用户业务逻辑实例
func NewUserLogic(userRepo repositories.IUserRepo, fileService service.IFileService) service.IUserService {
	return &userLogic{
		userRepo:    userRepo,
		fileService: fileService,
	}
}

// 确保 userLogic 实现了 IUserService 接口
var _ service.IUserService = &userLogic{}

// GetUserByID 根据ID获取用户信息
func (u *userLogic) GetUserByID(ctx context.Context, id int64) (*v1.UserInfoResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	user, err := u.userRepo.GetByID(id)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", id)}
		}
		return nil, fmt.Errorf("logic: failed to get user by id: %w", err)
	}

	return u.convertToUserResponse(user), nil
}

// GetUserByUsername 根据用户名获取用户信息
func (u *userLogic) GetUserByUsername(ctx context.Context, username string) (*v1.UserInfoResponse, error) {
	if username == "" {
		return nil, &ParameterError{Field: "username", Message: "is required"}
	}

	user, err := u.userRepo.GetByUsername(username)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("username=%s", username)}
		}
		return nil, fmt.Errorf("logic: failed to get user by username: %w", err)
	}

	return u.convertToUserResponse(user), nil
}

// GetUserByEmail 根据邮箱获取用户信息
func (u *userLogic) GetUserByEmail(ctx context.Context, email string) (*v1.UserInfoResponse, error) {
	if email == "" {
		return nil, &ParameterError{Field: "email", Message: "is required"}
	}

	user, err := u.userRepo.GetByEmail(email)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("email=%s", email)}
		}
		return nil, fmt.Errorf("logic: failed to get user by email: %w", err)
	}

	return u.convertToUserResponse(user), nil
}

// GetUserByOpenid 根据微信openid获取用户信息
func (u *userLogic) GetUserByOpenid(ctx context.Context, openid string) (*v1.UserInfoResponse, error) {
	if openid == "" {
		return nil, &ParameterError{Field: "openid", Message: "is required"}
	}

	user, err := u.userRepo.GetByOpenID(openid)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("openid=%s", openid)}
		}
		return nil, fmt.Errorf("logic: failed to get user by openid: %w", err)
	}

	return u.convertToUserResponse(user), nil
}

// CreateUser 创建新用户
func (u *userLogic) CreateUser(ctx context.Context, req *v1.UserCreateReq) (*v1.UserInfoResponse, error) {
	// 参数验证
	if req.Username == "" {
		return nil, &ParameterError{Field: "username", Message: "is required"}
	}
	if req.Email == "" {
		return nil, &ParameterError{Field: "email", Message: "is required"}
	}
	if req.Password == "" {
		return nil, &ParameterError{Field: "password", Message: "is required"}
	}
	if req.Role == "" {
		return nil, &ParameterError{Field: "role", Message: "is required"}
	}

	// 检查用户名是否已存在
	exists, err := u.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check username exists: %w", err)
	}
	if exists {
		return nil, &UserAlreadyExistsError{Field: "username", Value: req.Username}
	}

	// 检查邮箱是否已存在
	exists, err = u.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to check email exists: %w", err)
	}
	if exists {
		return nil, &UserAlreadyExistsError{Field: "email", Value: req.Email}
	}

	// 检查openid是否已存在（如果提供了）
	if req.Openid != "" {
		exists, err = u.userRepo.ExistsByOpenID(req.Openid)
		if err != nil {
			return nil, fmt.Errorf("logic: failed to check openid exists: %w", err)
		}
		if exists {
			return nil, &UserAlreadyExistsError{Field: "openid", Value: req.Openid}
		}
	}

	// 加密密码
	hashedPassword, err := pkg.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to hash password: %w", err)
	}

	// 创建用户实体
	user := entities.NewUser(req.Username, req.Email, hashedPassword, req.Role)
	if req.Status != nil {
		user.Status = *req.Status
	}
	if req.AvatarUrl != "" {
		user.SetAvatarURL(req.AvatarUrl)
	}
	if req.Openid != "" {
		user.OpenID = req.Openid
	}

	// 保存用户
	if err := u.userRepo.Create(user); err != nil {
		// 检查是否是用户已存在错误
		var userExists *repositories.UserExistsError
		if errors.As(err, &userExists) {
			return nil, &UserAlreadyExistsError{Field: userExists.Field, Value: userExists.Value}
		}
		return nil, fmt.Errorf("logic: failed to create user: %w", err)
	}

	return u.convertToUserResponse(user), nil
}

// UpdateUser 更新用户信息
func (u *userLogic) UpdateUser(ctx context.Context, req *v1.UserUpdateReq) (*v1.UserInfoResponse, error) {
	if req.ID <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 获取现有用户
	existingUser, err := u.userRepo.GetByID(req.ID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", req.ID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for update: %w", err)
	}

	// 检查用户名是否被其他用户使用
	if req.Username != "" && req.Username != existingUser.Username {
		exists, err := u.userRepo.ExistsByUsername(req.Username)
		if err != nil {
			return nil, fmt.Errorf("logic: failed to check username exists: %w", err)
		}
		if exists {
			return nil, &UserAlreadyExistsError{Field: "username", Value: req.Username}
		}
		existingUser.Username = req.Username
	}

	// 检查邮箱是否被其他用户使用
	if req.Email != "" && req.Email != existingUser.Email {
		exists, err := u.userRepo.ExistsByEmail(req.Email)
		if err != nil {
			return nil, fmt.Errorf("logic: failed to check email exists: %w", err)
		}
		if exists {
			return nil, &UserAlreadyExistsError{Field: "email", Value: req.Email}
		}
		existingUser.Email = req.Email
	}

	// 更新其他字段
	if req.Role != "" {
		existingUser.Role = req.Role
	}
	if req.Status != nil {
		existingUser.Status = *req.Status
	}
	if req.AvatarUrl != "" {
		existingUser.SetAvatarURL(req.AvatarUrl)
	}

	// 保存更新
	if err := u.userRepo.Update(existingUser); err != nil {
		return nil, fmt.Errorf("logic: failed to update user: %w", err)
	}

	return u.convertToUserResponse(existingUser), nil
}

// UpdateUserStatus 更新用户状态
func (u *userLogic) UpdateUserStatus(ctx context.Context, userID int64, req *v1.UserStatusUpdateReq) error {
	if userID <= 0 {
		return &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 检查用户是否存在
	_, err := u.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return fmt.Errorf("logic: failed to get user for status update: %w", err)
	}

	// 更新状态
	if err := u.userRepo.UpdateStatus(userID, req.Status); err != nil {
		return fmt.Errorf("logic: failed to update user status: %w", err)
	}

	return nil
}

// DeleteUser 删除用户（软删除）
func (u *userLogic) DeleteUser(ctx context.Context, id int64) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 检查用户是否存在
	_, err := u.userRepo.GetByID(id)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", id)}
		}
		return fmt.Errorf("logic: failed to get user for deletion: %w", err)
	}

	// 执行删除
	if err := u.userRepo.Delete(id); err != nil {
		return fmt.Errorf("logic: failed to delete user: %w", err)
	}

	return nil
}

// GetUserInfoPage 分页查询用户信息
func (u *userLogic) GetUserInfoPage(ctx context.Context, req *v1.UserQueryReq) (*v1.UserListResponse, error) {
	// 设置默认值
	current := req.Current
	if current <= 0 {
		current = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	// 计算偏移量
	offset := (current - 1) * size

	// 查询用户列表
	users, total, err := u.userRepo.List(offset, size)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get user list: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.UserInfoResponse, len(users))
	for i, user := range users {
		records[i] = u.convertToUserResponse(user)
	}

	return &v1.UserListResponse{
		Total:   total,
		Records: records,
		Current: current,
		Size:    size,
	}, nil
}

// UpdateUserAvatar 更新用户头像URL
func (u *userLogic) UpdateUserAvatar(ctx context.Context, userID int64, avatarURL string) error {
	if userID <= 0 {
		return &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 获取现有用户
	user, err := u.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return fmt.Errorf("logic: failed to get user for avatar update: %w", err)
	}

	// 更新头像URL
	user.SetAvatarURL(avatarURL)

	// 保存更新
	if err := u.userRepo.Update(user); err != nil {
		return fmt.Errorf("logic: failed to update user avatar: %w", err)
	}

	return nil
}

// VerifyPassword 验证用户密码
func (u *userLogic) VerifyPassword(ctx context.Context, usernameOrEmail, password string) (bool, error) {
	if usernameOrEmail == "" {
		return false, &ParameterError{Field: "usernameOrEmail", Message: "is required"}
	}
	if password == "" {
		return false, &ParameterError{Field: "password", Message: "is required"}
	}

	// 根据用户名或邮箱查找用户
	user, err := u.userRepo.GetByUsernameOrEmail(usernameOrEmail)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return false, nil // 用户不存在，返回false但不报错
		}
		return false, fmt.Errorf("logic: failed to get user for password verification: %w", err)
	}

	// 验证密码
	return pkg.VerifyPassword(user.Password, password), nil
}

// ChangePassword 修改用户密码
func (u *userLogic) ChangePassword(ctx context.Context, userID int64, req *v1.PasswordUpdateReq) error {
	if userID <= 0 {
		return &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 获取当前用户信息
	user, err := u.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return fmt.Errorf("logic: failed to get user for password change: %w", err)
	}

	// 验证旧密码
	if !pkg.VerifyPassword(user.Password, req.OldPassword) {
		return &InvalidCredentialsError{}
	}

	// 加密新密码
	hashedPassword, err := pkg.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("logic: failed to hash new password: %w", err)
	}

	// 更新密码
	if err := u.userRepo.UpdatePassword(userID, hashedPassword); err != nil {
		return fmt.Errorf("logic: failed to update password: %w", err)
	}

	return nil
}

// GenerateAvatarUploadURL 生成用户头像上传URL
func (u *userLogic) GenerateAvatarUploadURL(ctx context.Context, userID int64, contentType string) (*v1.AvatarResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}
	if contentType == "" {
		return nil, &ParameterError{Field: "contentType", Message: "is required"}
	}

	// 检查用户是否存在
	_, err := u.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for avatar upload: %w", err)
	}

	// 生成上传URL
	result, err := u.fileService.GenerateUploadPresignedURL(ctx, userID, "avatar", contentType, 60) // 60分钟有效期
	if err != nil {
		return nil, fmt.Errorf("logic: failed to generate avatar upload URL: %w", err)
	}

	// 解析结果（格式为 "presignedUrl:::fileName"）
	parts := strings.Split(result, ":::")
	if len(parts) != 2 {
		return nil, fmt.Errorf("logic: invalid upload URL result format")
	}

	return &v1.AvatarResponse{
		UploadUrl: parts[0],
		FileName:  parts[1],
	}, nil
}

// GenerateAvatarDownloadURL 生成用户头像下载URL
func (u *userLogic) GenerateAvatarDownloadURL(ctx context.Context, userID int64) (*v1.AvatarResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 获取用户信息
	user, err := u.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for avatar download: %w", err)
	}

	// 检查用户是否有头像
	if user.AvatarURL == "" {
		return &v1.AvatarResponse{}, nil // 返回空响应
	}

	// 生成下载URL
	downloadURL, err := u.fileService.GenerateDownloadPresignedURL(ctx, user.AvatarURL, 60) // 60分钟有效期
	if err != nil {
		return nil, fmt.Errorf("logic: failed to generate avatar download URL: %w", err)
	}

	return &v1.AvatarResponse{
		AvatarUrl: downloadURL,
	}, nil
}

// convertToUserResponse 将用户实体转换为响应格式
func (u *userLogic) convertToUserResponse(user *entities.User) *v1.UserInfoResponse {
	return &v1.UserInfoResponse{
		ID:         user.ID,
		Username:   user.Username,
		Role:       user.Role,
		Email:      user.Email,
		Status:     user.Status,
		CreateTime: user.CreateTime,
		AvatarUrl:  user.AvatarURL,
		Phone:      "", // 当前实体中没有phone字段，保持空值
	}
}

// userNutritionGoalLogic 用户营养目标业务逻辑实现
type userNutritionGoalLogic struct {
	userRepo          repositories.IUserRepo
	nutritionGoalRepo repositories.IUserNutritionGoalRepo // 需要添加这个仓储接口
}

// NewUserNutritionGoalLogic 创建用户营养目标业务逻辑实例
func NewUserNutritionGoalLogic(userRepo repositories.IUserRepo, nutritionGoalRepo repositories.IUserNutritionGoalRepo) service.IUserNutritionGoalService {
	return &userNutritionGoalLogic{
		userRepo:          userRepo,
		nutritionGoalRepo: nutritionGoalRepo,
	}
}

// 确保 userNutritionGoalLogic 实现了 IUserNutritionGoalService 接口
var _ service.IUserNutritionGoalService = &userNutritionGoalLogic{}

// GetNutritionGoal 根据用户ID获取营养目标
func (ung *userNutritionGoalLogic) GetNutritionGoal(ctx context.Context, userID int64) (*v1.UserNutritionGoalResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 检查用户是否存在
	_, err := ung.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for nutrition goal: %w", err)
	}

	// 获取营养目标
	goal, err := ung.nutritionGoalRepo.GetByUserID(userID)
	if err != nil {
		// 如果营养目标不存在，返回nil而不是错误
		var goalNotFound *repositories.UserNutritionGoalNotFoundError
		if errors.As(err, &goalNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("logic: failed to get nutrition goal: %w", err)
	}

	return ung.convertToNutritionGoalResponse(goal), nil
}

// CreateNutritionGoal 创建用户营养目标
func (ung *userNutritionGoalLogic) CreateNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalCreateReq) (*v1.UserNutritionGoalResponse, error) {
	if req.UserID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 检查用户是否存在
	_, err := ung.userRepo.GetByID(req.UserID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", req.UserID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for nutrition goal creation: %w", err)
	}

	// 检查是否已存在营养目标
	existingGoal, err := ung.nutritionGoalRepo.GetByUserID(req.UserID)
	if err == nil && existingGoal != nil {
		return nil, &ParameterError{Field: "userID", Message: "nutrition goal already exists for this user"}
	}

	// 创建营养目标实体
	goal := entities.NewUserNutritionGoals(req.UserID)
	if req.CalorieTarget != nil {
		goal.SetCalorieTarget(*req.CalorieTarget)
	}
	if req.WeightTarget != nil {
		goal.WeightTarget = req.WeightTarget
	}
	if req.ProteinTarget != nil {
		goal.ProteinTarget = req.ProteinTarget
	}
	if req.CarbsTarget != nil {
		goal.CarbsTarget = req.CarbsTarget
	}
	if req.FatTarget != nil {
		goal.FatTarget = req.FatTarget
	}
	goal.IsVegetarian = req.IsVegetarian
	goal.IsLowCarb = req.IsLowCarb
	goal.IsHighProtein = req.IsHighProtein
	goal.IsGlutenFree = req.IsGlutenFree
	goal.IsLowSodium = req.IsLowSodium

	// 保存营养目标
	if err := ung.nutritionGoalRepo.Create(goal); err != nil {
		return nil, fmt.Errorf("logic: failed to create nutrition goal: %w", err)
	}

	return ung.convertToNutritionGoalResponse(goal), nil
}

// UpdateNutritionGoal 更新用户营养目标
func (ung *userNutritionGoalLogic) UpdateNutritionGoal(ctx context.Context, req *v1.UserNutritionGoalUpdateReq) (*v1.UserNutritionGoalResponse, error) {
	if req.ID <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 获取现有营养目标
	existingGoal, err := ung.nutritionGoalRepo.GetByID(req.ID)
	if err != nil {
		var goalNotFound *repositories.UserNutritionGoalNotFoundError
		if errors.As(err, &goalNotFound) {
			return nil, &ParameterError{Field: "id", Message: "nutrition goal not found"}
		}
		return nil, fmt.Errorf("logic: failed to get nutrition goal for update: %w", err)
	}

	// 更新字段
	if req.CalorieTarget != nil {
		existingGoal.SetCalorieTarget(*req.CalorieTarget)
	}
	if req.WeightTarget != nil {
		existingGoal.WeightTarget = req.WeightTarget
	}
	if req.ProteinTarget != nil {
		existingGoal.ProteinTarget = req.ProteinTarget
	}
	if req.CarbsTarget != nil {
		existingGoal.CarbsTarget = req.CarbsTarget
	}
	if req.FatTarget != nil {
		existingGoal.FatTarget = req.FatTarget
	}
	existingGoal.IsVegetarian = req.IsVegetarian
	existingGoal.IsLowCarb = req.IsLowCarb
	existingGoal.IsHighProtein = req.IsHighProtein
	existingGoal.IsGlutenFree = req.IsGlutenFree
	existingGoal.IsLowSodium = req.IsLowSodium

	// 保存更新
	if err := ung.nutritionGoalRepo.Update(existingGoal); err != nil {
		return nil, fmt.Errorf("logic: failed to update nutrition goal: %w", err)
	}

	return ung.convertToNutritionGoalResponse(existingGoal), nil
}

// DeleteNutritionGoal 删除用户营养目标
func (ung *userNutritionGoalLogic) DeleteNutritionGoal(ctx context.Context, id int64) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 检查营养目标是否存在
	_, err := ung.nutritionGoalRepo.GetByID(id)
	if err != nil {
		var goalNotFound *repositories.UserNutritionGoalNotFoundError
		if errors.As(err, &goalNotFound) {
			return &ParameterError{Field: "id", Message: "nutrition goal not found"}
		}
		return fmt.Errorf("logic: failed to get nutrition goal for deletion: %w", err)
	}

	// 执行删除
	if err := ung.nutritionGoalRepo.Delete(id); err != nil {
		return fmt.Errorf("logic: failed to delete nutrition goal: %w", err)
	}

	return nil
}

// CreateDefaultNutritionGoal 创建默认营养目标
func (ung *userNutritionGoalLogic) CreateDefaultNutritionGoal(ctx context.Context, userID int64) (*v1.UserNutritionGoalResponse, error) {
	if userID <= 0 {
		return nil, &ParameterError{Field: "userID", Message: "must be positive"}
	}

	// 检查用户是否存在
	_, err := ung.userRepo.GetByID(userID)
	if err != nil {
		var userNotFound *repositories.UserNotFoundError
		if errors.As(err, &userNotFound) {
			return nil, &UserNotFoundError{Identifier: fmt.Sprintf("id=%d", userID)}
		}
		return nil, fmt.Errorf("logic: failed to get user for default nutrition goal creation: %w", err)
	}

	// 创建默认营养目标
	goal := entities.NewUserNutritionGoals(userID)
	// 设置默认值
	defaultCalorie := 2000
	goal.SetCalorieTarget(defaultCalorie)

	// 保存营养目标
	if err := ung.nutritionGoalRepo.Create(goal); err != nil {
		return nil, fmt.Errorf("logic: failed to create default nutrition goal: %w", err)
	}

	return ung.convertToNutritionGoalResponse(goal), nil
}

// convertToNutritionGoalResponse 将营养目标实体转换为响应格式
func (ung *userNutritionGoalLogic) convertToNutritionGoalResponse(goal *entities.UserNutritionGoals) *v1.UserNutritionGoalResponse {
	return &v1.UserNutritionGoalResponse{
		ID:            goal.ID,
		UserID:        goal.UserID,
		CalorieTarget: goal.CalorieTarget,
		WeightTarget:  goal.WeightTarget,
		ProteinTarget: goal.ProteinTarget,
		CarbsTarget:   goal.CarbsTarget,
		FatTarget:     goal.FatTarget,
		IsVegetarian:  goal.IsVegetarian,
		IsLowCarb:     goal.IsLowCarb,
		IsHighProtein: goal.IsHighProtein,
		IsGlutenFree:  goal.IsGlutenFree,
		IsLowSodium:   goal.IsLowSodium,
		CreatedAt:     goal.CreatedAt,
		UpdatedAt:     goal.UpdatedAt,
	}
}
